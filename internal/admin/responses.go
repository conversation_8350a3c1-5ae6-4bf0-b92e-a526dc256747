package admin

import (
	"context"
	"fmt"
	"strings"
	"time"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"

	"telegram-bot-api/internal/model"
	"telegram-bot-api/internal/model/entity"
	"telegram-bot-api/internal/service"
)

// BuildPlatformStatsMessage builds the platform statistics message
func BuildPlatformStatsMessage(ctx context.Context, stats *service.PlatformStats) string {
	if stats == nil {
		return service.I18n().T(ctx, "AdminPlatformStatsNoData")
	}

	return fmt.Sprintf(
		service.I18n().T(ctx, "AdminPlatformStatsFormat"),
		stats.TotalUsers,
		stats.TotalDeposits,
		stats.TotalWithdraws,
		stats.TotalCommission,
	)
}

// BuildPlatformStatsV2Message builds the comprehensive platform statistics message for V2
func BuildPlatformStatsV2Message(ctx context.Context, stats *service.PlatformStatsV2) string {
	if stats == nil {
		return service.I18n().T(ctx, "AdminPlatformStatsNoData")
	}

	var msg strings.Builder

	// Header
	msg.WriteString("📊 平台统计数据\n\n")

	// Daily statistics
	msg.WriteString(fmt.Sprintf("今日总存款：%s\n", stats.TodayDeposits.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("今日总取款：%s\n", stats.TodayWithdrawals.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("昨日总存款：%s\n", stats.YesterdayDeposits.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("昨日总取款：%s\n", stats.YesterdayWithdrawals.StringFixed(2)))

	// Weekly statistics
	msg.WriteString(fmt.Sprintf("本周总存款：%s\n", stats.WeekDeposits.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("本周总取款：%s\n", stats.WeekWithdrawals.StringFixed(2)))

	// Monthly statistics
	msg.WriteString(fmt.Sprintf("本月总存款：%s\n", stats.MonthDeposits.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("本月总取款：%s\n", stats.MonthWithdrawals.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("上月总存款：%s\n", stats.LastMonthDeposits.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("上月总取款：%s\n", stats.LastMonthWithdrawals.StringFixed(2)))

	// Admin manual adjustments
	msg.WriteString(fmt.Sprintf("本月管理员手动加额：%s\n", stats.MonthlyAdminDeposits.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("本月管理员手动减额：%s\n", stats.MonthlyAdminWithdraws.StringFixed(2)))

	// Bonus adjustments
	msg.WriteString(fmt.Sprintf("赠送彩金总金额：%s\n", stats.TotalBonusDeposits.StringFixed(2)))
	// msg.WriteString(fmt.Sprintf("总减额彩金：%s\n", stats.TotalBonusWithdraws.StringFixed(2)))

	// Lifetime totals
	msg.WriteString(fmt.Sprintf("总存款：%s\n", stats.TotalDeposits.StringFixed(2)))
	msg.WriteString(fmt.Sprintf("总取款：%s\n", stats.TotalWithdrawals.StringFixed(2)))

	// Separator
	msg.WriteString("--------------------------\n")

	// Provider statistics
	for _, provider := range stats.Providers {
		msg.WriteString(fmt.Sprintf("服务商：%s\n", provider.ProviderName))
		msg.WriteString(fmt.Sprintf("今日盈亏：%s\n", provider.TodayPnL.StringFixed(2)))
		msg.WriteString(fmt.Sprintf("昨日盈亏：%s\n", provider.YesterdayPnL.StringFixed(2)))
		msg.WriteString(fmt.Sprintf("本周盈亏：%s\n", provider.WeekPnL.StringFixed(2)))
		msg.WriteString(fmt.Sprintf("本月盈亏：%s\n", provider.MonthPnL.StringFixed(2)))
		msg.WriteString(fmt.Sprintf("上月盈亏：%s\n", provider.LastMonthPnL.StringFixed(2)))
		msg.WriteString(fmt.Sprintf("上上月盈亏：%s\n", provider.PrevMonthPnL.StringFixed(2)))
		msg.WriteString("\n")
	}

	// Footer
	msg.WriteString("💡所有单位为CNY")
	msg.WriteString(fmt.Sprintf("\n更新时间: %s", time.Now().String()))

	return msg.String()
}

// BuildPersonalStatsMessage builds the personal statistics message
func BuildPersonalStatsMessage(ctx context.Context, stats *service.PersonalStats) string {
	if stats == nil {
		return service.I18n().T(ctx, "AdminPersonalStatsNoData")
	}

	return fmt.Sprintf(
		service.I18n().T(ctx, "AdminPersonalStatsFormat"),
		stats.UserID,
		stats.TotalAmount,
	)
}

// BuildDailyReportMessage builds the daily report message
func BuildDailyReportMessage(ctx context.Context, report *service.DailyReport) string {
	if report == nil {
		return service.I18n().T(ctx, "AdminDailyReportNoData")
	}

	return fmt.Sprintf(
		service.I18n().T(ctx, "AdminDailyReportFormat"),
		report.Date,
		report.DailyDeposits,
		report.DailyWithdraws,
	)
}

// BuildMerchantInfoMessage builds the merchant information message
func BuildMerchantInfoMessage(ctx context.Context, info *service.MerchantInfo) string {
	if info == nil {
		return service.I18n().T(ctx, "AdminMerchantInfoNoData")
	}

	return fmt.Sprintf(
		service.I18n().T(ctx, "AdminMerchantInfoFormat"),
		info.MerchantID,
		info.MerchantName,
	)
}

// BuildMerchantStatsMessage builds the merchant statistics message
func BuildMerchantStatsMessage(ctx context.Context, stats *service.MerchantStats) string {
	var msg strings.Builder

	// Header
	msg.WriteString("🏪 商户信息\n\n")

	// Basic Info
	msg.WriteString("📋 基本信息\n")
	msg.WriteString(fmt.Sprintf("🏢 商户名称: %s\n", stats.MerchantName))
	msg.WriteString(fmt.Sprintf("🔖 租户ID: %d\n\n", stats.TenantID))

	// Balance and Fee Information
	msg.WriteString(fmt.Sprintf("总余额：%s CNY\n", formatDecimal(stats.TotalBalance)))
	msg.WriteString(fmt.Sprintf("今日取款手续费：%s CNY\n", formatDecimal(stats.TodayFees)))
	msg.WriteString(fmt.Sprintf("昨日取款手续费：%s CNY\n", formatDecimal(stats.YesterdayFees)))
	msg.WriteString(fmt.Sprintf("所有取款手续费：%s CNY", formatDecimal(stats.TotalFees)))

	return msg.String()
}

// formatDecimal formats a decimal number with thousand separators
func formatDecimal(d decimal.Decimal) string {
	// Convert to string with 2 decimal places
	str := d.StringFixed(2)

	// Split by decimal point
	parts := strings.Split(str, ".")
	intPart := parts[0]
	decPart := ""
	if len(parts) > 1 {
		decPart = parts[1]
	}

	// Add thousand separators to integer part
	// Convert integer part to runes for easier manipulation
	runes := []rune(intPart)
	isNegative := false
	if len(runes) > 0 && runes[0] == '-' {
		isNegative = true
		runes = runes[1:]
	}

	// Add commas from right to left
	var result []rune
	for i := len(runes) - 1; i >= 0; i-- {
		result = append([]rune{runes[i]}, result...)
		if (len(runes)-i)%3 == 0 && i > 0 {
			result = append([]rune{','}, result...)
		}
	}

	// Reconstruct the number
	formatted := string(result)
	if isNegative {
		formatted = "-" + formatted
	}
	if decPart != "" {
		formatted = formatted + "." + decPart
	}

	return formatted
}

// BuildDepositLogsMessage builds the deposit logs message
func BuildDepositLogsMessage(ctx context.Context, logs *service.LogResult) string {
	if logs == nil || len(logs.Items) == 0 {
		return service.I18n().T(ctx, "AdminDepositLogsNoData")
	}

	header := service.I18n().T(ctx, "AdminDepositLogsHeader")

	// TODO: Format actual log items
	// For now, show basic information
	return fmt.Sprintf("%s\n%s", header,
		service.I18n().Tf(ctx, "AdminLogsPaginationInfo", logs.Page, logs.TotalCount))
}

// BuildWithdrawLogsMessage builds the withdraw logs message
func BuildWithdrawLogsMessage(ctx context.Context, logs *service.LogResult) string {
	if logs == nil || len(logs.Items) == 0 {
		return service.I18n().T(ctx, "AdminWithdrawLogsNoData")
	}

	header := service.I18n().T(ctx, "AdminWithdrawLogsHeader")

	// TODO: Format actual log items
	// For now, show basic information
	return fmt.Sprintf("%s\n%s", header,
		service.I18n().Tf(ctx, "AdminLogsPaginationInfo", logs.Page, logs.TotalCount))
}

// BuildWithdrawalDetailsMessage builds the withdrawal approval details message
func BuildWithdrawalDetailsMessage(ctx context.Context, withdrawal *entity.UserWithdraws, currentPage, totalCount int) string {
	if withdrawal == nil {
		return service.I18n().T(ctx, "AdminWithdrawalDetailsNoData")
	}

	// Build detailed message with all metadata
	msg := fmt.Sprintf("📋 <b>%s</b> (%d/%d)\n\n",
		service.I18n().T(ctx, "AdminWithdrawalDetailsHeader"),
		currentPage,
		totalCount)

	// Withdrawal ID and Order Info
	msg += fmt.Sprintf("🆔 <b>%s</b>\n", service.I18n().T(ctx, "AdminWithdrawalInfo"))
	msg += fmt.Sprintf("%s: #%d\n", service.I18n().T(ctx, "AdminWithdrawalID"), withdrawal.UserWithdrawsId)
	msg += fmt.Sprintf("%s: %s\n", service.I18n().T(ctx, "AdminOrderNo"), withdrawal.OrderNo)
	if withdrawal.PaybotOrderNo != "" {
		msg += fmt.Sprintf("%s: %s\n", service.I18n().T(ctx, "AdminPaybotOrderNo"), withdrawal.PaybotOrderNo)
	}
	msg += "\n"

	// User Information
	msg += fmt.Sprintf("👤 <b>%s</b>\n", service.I18n().T(ctx, "AdminUserInfo"))
	msg += fmt.Sprintf("%s: %d\n", service.I18n().T(ctx, "AdminUserID"), withdrawal.UserId)

	// Get user details if available
	user, _ := service.User().GetUserByUserId(ctx, uint64(withdrawal.UserId))
	if user != nil {
		msg += fmt.Sprintf("%s: %s\n", service.I18n().T(ctx, "AdminUsername"), user.Account)
		if user.IsStop == 1 {
			msg += fmt.Sprintf("%s: ⚠️ %s\n", service.I18n().T(ctx, "AdminStatus"), service.I18n().T(ctx, "AdminUserSuspended"))
		}
	}
	msg += "\n"

	// Financial Details
	msg += fmt.Sprintf("💰 <b>%s</b>\n", service.I18n().T(ctx, "AdminFinancialDetails"))
	msg += fmt.Sprintf("%s: %s %s\n", service.I18n().T(ctx, "AdminAmount"), withdrawal.Amount.String(), withdrawal.Name)
	msg += fmt.Sprintf("%s: %s %s\n", service.I18n().T(ctx, "AdminHandlingFee"), withdrawal.HandlingFee.String(), withdrawal.Name)
	msg += fmt.Sprintf("%s: %s %s\n", service.I18n().T(ctx, "AdminActualAmount"), withdrawal.ActualAmount.String(), withdrawal.Name)
	if !withdrawal.ConvertedAmount.IsZero() {
		msg += fmt.Sprintf("%s: %s %s (Rate: %s)\n",
			service.I18n().T(ctx, "AdminConvertedAmount"),
			withdrawal.ConvertedAmount.String(),
			withdrawal.ConversionTokenSymbol,
			withdrawal.ConversionRate.String())
	}
	msg += "\n"

	// Recipient Information
	msg += fmt.Sprintf("📬 <b>%s</b>\n", service.I18n().T(ctx, "AdminRecipientInfo"))
	if withdrawal.WithdrawType == "address_pay" {
		msg += fmt.Sprintf("%s: <code>%s</code>\n", service.I18n().T(ctx, "AdminAddress"), withdrawal.Address)
		msg += fmt.Sprintf("%s: %s\n", service.I18n().T(ctx, "AdminChan"), withdrawal.Chan)
	} else if withdrawal.WithdrawType == "auth_pay" {
		if withdrawal.RecipientName != "" {
			msg += fmt.Sprintf("%s: %s\n", service.I18n().T(ctx, "AdminRecipientName"), withdrawal.RecipientName)
		}
		if withdrawal.RecipientAccount != "" {
			msg += fmt.Sprintf("%s: %s\n", service.I18n().T(ctx, "AdminRecipientAccount"), withdrawal.RecipientAccount)
		}
		if withdrawal.FiatType != "" {
			msg += fmt.Sprintf("%s: %s\n", service.I18n().T(ctx, "AdminFiatType"), withdrawal.FiatType)
		}
	}
	msg += "\n"

	// Status and Timestamps
	msg += fmt.Sprintf("⏱ <b>%s</b>\n", service.I18n().T(ctx, "AdminTimestamps"))
	msg += fmt.Sprintf("%s: %s\n", service.I18n().T(ctx, "AdminCreatedAt"), withdrawal.CreatedAt.String())
	if withdrawal.CheckedAt != nil {
		msg += fmt.Sprintf("%s: %s\n", service.I18n().T(ctx, "AdminCheckedAt"), withdrawal.CheckedAt.String())
	}
	if withdrawal.ProcessingAt != nil {
		msg += fmt.Sprintf("%s: %s\n", service.I18n().T(ctx, "AdminProcessingAt"), withdrawal.ProcessingAt.String())
	}

	// User Remark if exists
	if withdrawal.UserRemark != "" {
		msg += fmt.Sprintf("\n📝 <b>%s</b>\n%s\n", service.I18n().T(ctx, "AdminUserRemark"), withdrawal.UserRemark)
	}

	return msg
}

// sendAdminReply sends a message as a reply to the user's input message
func sendAdminReply(ctx context.Context, chatID int64, replyToMessageID int, text string) (bool, error) {
	bot, err := getTenantBot(ctx)
	if err != nil {
		return true, err
	}

	msg := tgbotapi.NewMessage(chatID, text)
	msg.ParseMode = "HTML"
	msg.ReplyToMessageID = replyToMessageID
	_, err = bot.Send(msg)

	return true, err
}

// sendAdminReplyWithKeyboard sends a message with keyboard as a reply to the user's input
func sendAdminReplyWithKeyboard(ctx context.Context, chatID int64, replyToMessageID int, text string, keyboard tgbotapi.InlineKeyboardMarkup) (bool, error) {
	bot, err := getTenantBot(ctx)
	if err != nil {
		return true, err
	}

	msg := tgbotapi.NewMessage(chatID, text)
	msg.ParseMode = "HTML"
	msg.ReplyToMessageID = replyToMessageID
	msg.ReplyMarkup = keyboard
	_, err = bot.Send(msg)

	return true, err
}

// sendErrorReplyAndKeepState sends an error message as a reply while preserving user state
func sendErrorReplyAndKeepState(ctx context.Context, chatID int64, replyToMessageID int, telegramUserID int64, userState *model.UserState, errorText string) (bool, error) {
	// Build back to admin keyboard
	keyboard := BuildBackToAdminKeyboard(ctx)

	// Add retry instruction to error message
	fullErrorText := errorText + "\n\n" + service.I18n().T(ctx, "AdminRetryInputPrompt")

	// Send as reply to user's message
	bot, err := getTenantBot(ctx)
	if err != nil {
		return true, err
	}

	msg := tgbotapi.NewMessage(chatID, fullErrorText)
	msg.ParseMode = "HTML"
	msg.ReplyToMessageID = replyToMessageID
	msg.ReplyMarkup = keyboard
	_, err = bot.Send(msg)
	
	if err != nil {
		g.Log().Errorf(ctx, "Failed to send error reply: %v", err)
		return true, err
	}

	// Increment retry count and update state in Redis
	if userState != nil {
		userState.RetryCount++
		err = service.UserState().SetUserStateByTelegramId(ctx, telegramUserID, userState)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to update user state retry count: %v", err)
		}
	}

	return true, nil
}
