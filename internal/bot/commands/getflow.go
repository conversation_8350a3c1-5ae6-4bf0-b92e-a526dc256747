package commands

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"telegram-bot-api/internal/model/entity"
	"telegram-bot-api/internal/service"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/i18n/gi18n"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
	"github.com/yalks/wallet"
)

// GetFlowCommandResponse represents the response for the getflow command
type GetFlowCommandResponse struct {
	ChatID         int64                          `json:"chat_id"`
	Text           string                         `json:"text"`
	ParseMode      string                         `json:"parse_mode,omitempty"`
	InlineKeyboard *tgbotapi.InlineKeyboardMarkup `json:"reply_markup,omitempty"`
}

// GetChatID returns the chat ID
func (r *GetFlowCommandResponse) GetChatID() int64 {
	return r.ChatID
}

// GetText returns the response text
func (r *GetFlowCommandResponse) GetText() string {
	return r.Text
}

// GetParseMode returns the parse mode
func (r *GetFlowCommandResponse) GetParseMode() string {
	return r.ParseMode
}

// GetInlineKeyboard returns the inline keyboard
func (r *GetFlowCommandResponse) GetInlineKeyboard() *tgbotapi.InlineKeyboardMarkup {
	return r.InlineKeyboard
}

// GetReplyMarkup returns the reply markup (required by CommandResponse interface)
func (r *GetFlowCommandResponse) GetReplyMarkup() *tgbotapi.InlineKeyboardMarkup {
	return r.InlineKeyboard
}

// HandleGetFlowCommand processes the /getflow command and returns balance information
func HandleGetFlowCommand(ctx context.Context, update *tgbotapi.Update) (CommandResponse, error) {
	chat := update.Message.Chat
	userID := update.Message.From.ID

	g.Log().Infof(ctx, "Handling /getflow command from user %d", userID)

	// Get i18n service instance
	i18n := service.I18n().Instance()

	// Get user information
	user, err := service.User().GetUserByTelegramId(ctx, userID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get user %d: %v", userID, err)
		return &GetFlowCommandResponse{
			ChatID:    chat.ID,
			Text:      i18n.T(ctx, "{#SystemError}"),
			ParseMode: "HTML",
		}, nil
	}

	if user == nil {
		g.Log().Errorf(ctx, "User %d not found", userID)
		return &GetFlowCommandResponse{
			ChatID:    chat.ID,
			Text:      i18n.T(ctx, "{#UserNotFound}"),
			ParseMode: "HTML",
		}, nil
	}

	// Build the flow information text
	flowText, err := buildFlowText(ctx, user, i18n)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to build flow text for user %d: %v", userID, err)
		return &GetFlowCommandResponse{
			ChatID:    chat.ID,
			Text:      i18n.T(ctx, "{#SystemError}"),
			ParseMode: "HTML",
		}, nil
	}

	// Create inline keyboard with back button
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(i18n.T(ctx, "BackButton"), "back_to_start"),
		),
	)

	return &GetFlowCommandResponse{
		ChatID:         chat.ID,
		Text:           flowText,
		ParseMode:      "HTML",
		InlineKeyboard: &keyboard,
	}, nil
}

// buildFlowText builds the flow information text with local wallet and game balances
func buildFlowText(ctx context.Context, user *entity.Users, i18n *gi18n.Manager) (string, error) {
	var textBuilder strings.Builder

	// Add header
	textBuilder.WriteString("💰 <b>资金流水信息</b>\n\n")

	// 1. Get local wallet balance
	localBalance := decimal.Zero
	balanceInfo, err := wallet.Manager().GetBalance(ctx, uint64(user.Id), "CNY")
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get local wallet balance for user %d: %v", user.Id, err)
		textBuilder.WriteString("💳 <b>本地钱包余额:</b> <code>获取失败</code>\n\n")
	} else if balanceInfo != nil {
		localBalance = balanceInfo.AvailableBalance
		textBuilder.WriteString(fmt.Sprintf("💳 <b>本地钱包余额:</b> <code>%s</code> CNY\n\n", localBalance.StringFixed(2)))
	} else {
		textBuilder.WriteString("💳 <b>本地钱包余额:</b> <code>0.00</code> CNY\n\n")
	}

	// 2. Get game provider balances
	textBuilder.WriteString("🎮 <b>游戏余额详情:</b>\n")

	// Get the list of unique providers from game_catalog with their display names and product codes
	// Only include games with numeric product codes since GameBalance service requires int
	var providerTypes []struct {
		ProductType string `json:"product_type"`
		ProductCode string `json:"product_code"`
		GameName    string `json:"game_name"`
	}

	err = g.DB().Model("game_catalog").
		Fields("DISTINCT product_type, product_code, game_name").
		Where("display_status = ?", 1).
		Where("product_type IS NOT NULL AND product_type != ''").
		Where("product_code IS NOT NULL AND product_code != ''").
		Where("product_code REGEXP ?", "^[0-9]+$"). // Only numeric product codes
		Scan(&providerTypes)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get provider types: %v", err)
		textBuilder.WriteString("❌ 获取游戏提供商失败\n\n")
	} else if len(providerTypes) == 0 {
		textBuilder.WriteString("📝 暂无可用的游戏提供商\n\n")
	} else {
		totalGameBalance := decimal.Zero
		gameBalanceCount := 0

		// For each provider, get the balance
		for _, provider := range providerTypes {
			// 使用GameBalance服务获取指定游戏的余额
			productCode, err := strconv.Atoi(provider.ProductCode)
			if err != nil {
				g.Log().Errorf(ctx, "Invalid product code %s for game %s: %v", provider.ProductCode, provider.GameName, err)
				// 跳过无效的产品代码
				continue
			}

			gameBalance := "0.00"
			if user != nil {
				balance, err := service.GameBalance().GetUserGameBalance(ctx, uint64(user.Id), productCode)
				if err != nil {
					g.Log().Errorf(ctx, "Failed to get game balance for user %d, product %d: %v", user.Id, productCode, err)
					// 继续使用默认的 0.00 余额
				} else {
					gameBalance = balance.StringFixed(2)
					totalGameBalance = totalGameBalance.Add(balance)
					if balance.GreaterThan(decimal.Zero) {
						gameBalanceCount++
					}
				}
			}

			// Only show non-zero balances or if there are few providers
			if gameBalance != "0.00" || len(providerTypes) <= 3 {
				textBuilder.WriteString(fmt.Sprintf("  • %s: <code>%s</code> CNY\n", provider.GameName, gameBalance))
			}
		}

		// Add total game balance
		textBuilder.WriteString(fmt.Sprintf("\n🎯 <b>游戏余额总计:</b> <code>%s</code> CNY\n\n", totalGameBalance.StringFixed(2)))

		// Add overall total
		overallTotal := localBalance.Add(totalGameBalance)
		textBuilder.WriteString(fmt.Sprintf("💎 <b>总余额:</b> <code>%s</code> CNY\n\n", overallTotal.StringFixed(2)))
	}

	// Add withdraw betting volume requirement if exists
	if !user.WithdrawBettingVolume.IsZero() {
		textBuilder.WriteString(fmt.Sprintf("🎯 <b>当前剩余流水要求:</b> <code>%s</code> CNY\n\n", user.WithdrawBettingVolume.StringFixed(2)))
	}

	// Add footer with timestamp
	textBuilder.WriteString("🕐 <i>更新时间: " + gtime.Now().String() + "</i>")

	return textBuilder.String(), nil
}
