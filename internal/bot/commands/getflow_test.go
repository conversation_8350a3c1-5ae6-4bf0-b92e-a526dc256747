package commands

import (
	"strings"
	"testing"

	"telegram-bot-api/internal/model/entity"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

// TestWithdrawBettingVolumeDisplay tests the withdraw betting volume display logic
func TestWithdrawBettingVolumeDisplay(t *testing.T) {
	// Test case 1: User with withdraw betting volume requirement
	userWithRequirement := &entity.Users{
		Id:                    1,
		WithdrawBettingVolume: decimal.NewFromFloat(100.50),
	}

	var textBuilder strings.Builder
	textBuilder.WriteString("💰 资金流水信息\n\n")

	// Add withdraw betting volume requirement if exists
	if !userWithRequirement.WithdrawBettingVolume.IsZero() {
		textBuilder.WriteString("🎯 当前剩余流水要求: " + userWithRequirement.WithdrawBettingVolume.StringFixed(2) + " CNY\n\n")
	}

	result := textBuilder.String()
	assert.Contains(t, result, "当前剩余流水要求")
	assert.Contains(t, result, "100.50")

	// Test case 2: User without withdraw betting volume requirement
	userWithoutRequirement := &entity.Users{
		Id:                    2,
		WithdrawBettingVolume: decimal.Zero,
	}

	var textBuilder2 strings.Builder
	textBuilder2.WriteString("💰 资金流水信息\n\n")

	// Add withdraw betting volume requirement if exists
	if !userWithoutRequirement.WithdrawBettingVolume.IsZero() {
		textBuilder2.WriteString("🎯 当前剩余流水要求: " + userWithoutRequirement.WithdrawBettingVolume.StringFixed(2) + " CNY\n\n")
	}

	result2 := textBuilder2.String()
	assert.NotContains(t, result2, "当前剩余流水要求")
}

// TestWithdrawBettingVolumeZero tests zero withdraw betting volume
func TestWithdrawBettingVolumeZero(t *testing.T) {
	// Test case: User with zero withdraw betting volume
	user := &entity.Users{
		Id:                    3,
		WithdrawBettingVolume: decimal.NewFromFloat(0.00),
	}

	var textBuilder strings.Builder
	textBuilder.WriteString("💰 资金流水信息\n\n")

	// Add withdraw betting volume requirement if exists
	if !user.WithdrawBettingVolume.IsZero() {
		textBuilder.WriteString("🎯 当前剩余流水要求: " + user.WithdrawBettingVolume.StringFixed(2) + " CNY\n\n")
	}

	result := textBuilder.String()
	assert.NotContains(t, result, "当前剩余流水要求")
}
